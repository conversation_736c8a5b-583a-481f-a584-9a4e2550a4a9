#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Script monitoring cho cơ chế đồng bộ 15 phút
Sử dụng: python monitor_15min_sync.py
"""

import logging
from datetime import datetime, timedelta

_logger = logging.getLogger(__name__)


class ThSyncMonitor:
    """Class để monitor hệ thống đồng bộ 15 phút"""
    
    def __init__(self, env):
        self.env = env
        
    def th_check_automation_status(self):
        """Kiểm tra trạng thái các automation"""
        print("\n=== KIỂM TRA AUTOMATION STATUS ===")
        
        # Kiểm tra automation quarter_hour
        try:
            th_quarter_automation = self.env.ref('th_sync_fastapi.th_odoo_trigger_crm_update_quarter_hour')
            print(f"✓ Quarter Hour Automation: {'ACTIVE' if th_quarter_automation.active else 'INACTIVE'}")
            print(f"  - Trigger fields: {len(th_quarter_automation.trigger_field_ids)} fields")
            print(f"  - Fields: {', '.join(th_quarter_automation.trigger_field_ids.mapped('name'))}")
        except Exception as e:
            print(f"✗ Quarter Hour Automation: ERROR - {e}")
            
        # Kiểm tra automation ontime (should be disabled)
        try:
            th_ontime_automation = self.env.ref('th_sync_fastapi.th_odoo_trigger_crm_update_ontime')
            print(f"✓ Ontime Automation: {'ACTIVE (WARNING!)' if th_ontime_automation.active else 'DISABLED (OK)'}")
        except Exception as e:
            print(f"✗ Ontime Automation: ERROR - {e}")
            
    def th_check_scheduled_action_status(self):
        """Kiểm tra trạng thái scheduled action"""
        print("\n=== KIỂM TRA SCHEDULED ACTION STATUS ===")
        
        try:
            th_cron = self.env.ref('th_sync_fastapi.th_schedule_sync_fastapi_crm_update_quarter_hour')
            print(f"✓ Scheduled Action: {'ACTIVE' if th_cron.active else 'INACTIVE'}")
            print(f"  - Interval: {th_cron.interval_number} {th_cron.interval_type}")
            print(f"  - Next execution: {th_cron.nextcall}")
            print(f"  - Last execution: {th_cron.lastcall or 'Never'}")
        except Exception as e:
            print(f"✗ Scheduled Action: ERROR - {e}")
            
    def th_check_clipboard_status(self):
        """Kiểm tra trạng thái clipboard"""
        print("\n=== KIỂM TRA CLIPBOARD STATUS ===")
        
        # Thống kê clipboard theo status
        th_clipboard_stats = self.env['th.clipboard'].read_group(
            [('th_model_name', '=', 'crm.lead'), ('th_type_sync', '=', 'quarter_hour')],
            ['th_status'],
            ['th_status']
        )
        
        print("Clipboard Statistics (quarter_hour):")
        for stat in th_clipboard_stats:
            print(f"  - {stat['th_status']}: {stat['th_status_count']} records")
            
        # Kiểm tra clipboard cũ (> 1 giờ)
        th_old_threshold = datetime.now() - timedelta(hours=1)
        th_old_clipboard = self.env['th.clipboard'].search_count([
            ('th_model_name', '=', 'crm.lead'),
            ('th_type_sync', '=', 'quarter_hour'),
            ('th_status', '=', 'waiting'),
            ('create_date', '<', th_old_threshold)
        ])
        
        if th_old_clipboard > 0:
            print(f"⚠️  WARNING: {th_old_clipboard} clipboard records đang chờ > 1 giờ")
        else:
            print("✓ Không có clipboard records cũ")
            
    def th_check_recent_sync_activity(self):
        """Kiểm tra hoạt động đồng bộ gần đây"""
        print("\n=== KIỂM TRA HOẠT ĐỘNG ĐỒNG BỘ GẦN ĐÂY ===")
        
        # Kiểm tra 24 giờ qua
        th_last_24h = datetime.now() - timedelta(hours=24)
        
        # Success syncs
        th_success_count = self.env['th.clipboard'].search_count([
            ('th_model_name', '=', 'crm.lead'),
            ('th_type_sync', '=', 'quarter_hour'),
            ('th_status', '=', 'success'),
            ('write_date', '>=', th_last_24h)
        ])
        
        # Error syncs
        th_error_count = self.env['th.clipboard'].search_count([
            ('th_model_name', '=', 'crm.lead'),
            ('th_type_sync', '=', 'quarter_hour'),
            ('th_status', '=', 'error'),
            ('write_date', '>=', th_last_24h)
        ])
        
        print(f"Last 24 hours:")
        print(f"  - Successful syncs: {th_success_count}")
        print(f"  - Failed syncs: {th_error_count}")
        
        if th_error_count > 0:
            print(f"⚠️  WARNING: {th_error_count} failed syncs in last 24h")
            
            # Hiển thị một số lỗi gần đây
            th_recent_errors = self.env['th.clipboard'].search([
                ('th_model_name', '=', 'crm.lead'),
                ('th_type_sync', '=', 'quarter_hour'),
                ('th_status', '=', 'error'),
                ('write_date', '>=', th_last_24h)
            ], limit=5, order='write_date desc')
            
            print("Recent errors:")
            for error in th_recent_errors:
                print(f"  - ID {error.th_internal_id}: {error.response_data[:100]}...")
                
    def th_check_performance_metrics(self):
        """Kiểm tra metrics hiệu suất"""
        print("\n=== KIỂM TRA HIỆU SUẤT ===")
        
        # Thời gian xử lý trung bình
        th_recent_success = self.env['th.clipboard'].search([
            ('th_model_name', '=', 'crm.lead'),
            ('th_type_sync', '=', 'quarter_hour'),
            ('th_status', '=', 'success'),
            ('request_time', '!=', False),
            ('response_time', '!=', False)
        ], limit=100, order='write_date desc')
        
        if th_recent_success:
            th_processing_times = []
            for record in th_recent_success:
                if record.request_time and record.response_time:
                    th_diff = (record.response_time - record.request_time).total_seconds()
                    th_processing_times.append(th_diff)
            
            if th_processing_times:
                th_avg_time = sum(th_processing_times) / len(th_processing_times)
                th_max_time = max(th_processing_times)
                print(f"Processing time (last 100 records):")
                print(f"  - Average: {th_avg_time:.2f}s")
                print(f"  - Maximum: {th_max_time:.2f}s")
                
                if th_avg_time > 5.0:
                    print("⚠️  WARNING: Average processing time > 5s")
                if th_max_time > 30.0:
                    print("⚠️  WARNING: Maximum processing time > 30s")
        else:
            print("No recent successful syncs found")
            
    def th_check_data_consistency(self):
        """Kiểm tra tính nhất quán dữ liệu"""
        print("\n=== KIỂM TRA TÍNH NHẤT QUÁN DỮ LIỆU ===")
        
        # Kiểm tra CRM leads có thay đổi gần đây nhưng chưa sync
        th_recent_threshold = datetime.now() - timedelta(minutes=20)
        
        # Tìm leads được update gần đây
        th_recent_leads = self.env['crm.lead'].search([
            ('write_date', '>=', th_recent_threshold)
        ])
        
        th_unsynced_count = 0
        for lead in th_recent_leads:
            # Kiểm tra có clipboard waiting không
            th_clipboard = self.env['th.clipboard'].search([
                ('th_internal_id', '=', lead.id),
                ('th_model_name', '=', 'crm.lead'),
                ('th_type_sync', '=', 'quarter_hour'),
                ('th_status', 'in', ['waiting', 'pending'])
            ])
            
            if not th_clipboard:
                th_unsynced_count += 1
                
        print(f"Recent leads (last 20 min): {len(th_recent_leads)}")
        print(f"Unsynced leads: {th_unsynced_count}")
        
        if th_unsynced_count > 0:
            print(f"⚠️  WARNING: {th_unsynced_count} leads may not be synced")
            
    def th_run_full_check(self):
        """Chạy tất cả các kiểm tra"""
        print("=" * 60)
        print("MONITOR ĐỒNG BỘ 15 PHÚT - CRM LEAD")
        print(f"Thời gian: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        self.th_check_automation_status()
        self.th_check_scheduled_action_status()
        self.th_check_clipboard_status()
        self.th_check_recent_sync_activity()
        self.th_check_performance_metrics()
        self.th_check_data_consistency()
        
        print("\n" + "=" * 60)
        print("HOÀN THÀNH KIỂM TRA")
        print("=" * 60)
        
    def th_fix_common_issues(self):
        """Sửa một số vấn đề thường gặp"""
        print("\n=== TỰ ĐỘNG SỬA LỖI ===")
        
        # Xóa clipboard cũ (> 7 ngày)
        th_old_threshold = datetime.now() - timedelta(days=7)
        th_old_clipboard = self.env['th.clipboard'].search([
            ('th_model_name', '=', 'crm.lead'),
            ('th_type_sync', '=', 'quarter_hour'),
            ('th_status', 'in', ['success', 'error']),
            ('write_date', '<', th_old_threshold)
        ])
        
        if th_old_clipboard:
            th_old_clipboard.unlink()
            print(f"✓ Đã xóa {len(th_old_clipboard)} clipboard records cũ")
        else:
            print("✓ Không có clipboard records cũ cần xóa")
            
        # Reset clipboard bị stuck (pending > 1 giờ)
        th_stuck_threshold = datetime.now() - timedelta(hours=1)
        th_stuck_clipboard = self.env['th.clipboard'].search([
            ('th_model_name', '=', 'crm.lead'),
            ('th_type_sync', '=', 'quarter_hour'),
            ('th_status', '=', 'pending'),
            ('write_date', '<', th_stuck_threshold)
        ])
        
        if th_stuck_clipboard:
            th_stuck_clipboard.write({'th_status': 'waiting'})
            print(f"✓ Đã reset {len(th_stuck_clipboard)} clipboard records bị stuck")
        else:
            print("✓ Không có clipboard records bị stuck")


def th_main():
    """Hàm main để chạy monitor"""
    try:
        # Import Odoo environment (cần chạy trong context Odoo)
        import odoo
        from odoo import api, SUPERUSER_ID
        
        # Kết nối database
        db_name = 'your_database_name'  # Thay đổi tên database
        registry = odoo.registry(db_name)
        
        with registry.cursor() as cr:
            env = api.Environment(cr, SUPERUSER_ID, {})
            
            monitor = ThSyncMonitor(env)
            monitor.th_run_full_check()
            
            # Tùy chọn: tự động sửa lỗi
            th_auto_fix = input("\nBạn có muốn tự động sửa các lỗi thường gặp? (y/N): ")
            if th_auto_fix.lower() == 'y':
                monitor.th_fix_common_issues()
                
    except Exception as e:
        print(f"Lỗi khi chạy monitor: {e}")
        

if __name__ == '__main__':
    th_main()
