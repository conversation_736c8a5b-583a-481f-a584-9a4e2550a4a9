<?xml version="1.0" encoding="utf-8"?>
<odoo noupdate="0">
    <record id="th_odoo_trigger_source_group_update" model="base.automation">
        <field name="name">Base Automation: Create or Write Source Group</field>
        <field name="model_id" ref="model_th_source_group"/>
        <field name="state">code</field>
        <field name="code">
if not env.context.get('th_sync'):
    record.th_trigger_source_group(record, 'create_or_update')
        </field>
        <field name="trigger_field_ids" eval="[(6, 0, [
            ref('th_setup_parameters.field_th_source_group__name'),
            ref('th_setup_parameters.field_th_source_group__th_description'),
        ])]" />
        <field name="trigger">on_create_or_write</field>
        <field name="active">1</field>
    </record>

    <record id="th_odoo_trigger_source_group_delete" model="base.automation">
        <field name="name">Base Automation: Delete Source Group</field>
        <field name="model_id" ref="model_th_source_group"/>
        <field name="state">code</field>
        <field name="code">
if not env.context.get('th_sync'):
    record.th_trigger_source_group(record, 'delete')
        </field>
        <field name="trigger">on_unlink</field>
        <field name="active">1</field>
    </record>

    <record id="th_odoo_trigger_graduation_system_update" model="base.automation">
        <field name="name">Base Automation: Create or Write Graduation System</field>
        <field name="model_id" ref="model_th_graduation_system"/>
        <field name="state">code</field>
        <field name="code">
if not env.context.get('th_sync'):
    record.th_trigger_graduation_system(record, 'create_or_update')
        </field>
        <field name="trigger_field_ids" eval="[(6, 0, [
            ref('th_setup_parameters.field_th_source_group__name'),
            ref('th_setup_parameters.field_th_source_group__th_description'),
        ])]" />
        <field name="trigger">on_create_or_write</field>
        <field name="active">1</field>
    </record>

    <record id="th_odoo_trigger_graduation_system_delete" model="base.automation">
        <field name="name">Base Automation: Delete Graduation System</field>
        <field name="model_id" ref="model_th_graduation_system"/>
        <field name="state">code</field>
        <field name="code">
if not env.context.get('th_sync'):
    record.th_trigger_graduation_system(record, 'delete')
        </field>
        <field name="trigger">on_unlink</field>
        <field name="active">1</field>
    </record>

    <record id="th_odoo_trigger_major_update" model="base.automation">
        <field name="name">Base Automation: Create or Write Major</field>
        <field name="model_id" ref="model_th_major"/>
        <field name="state">code</field>
        <field name="code">
if not env.context.get('th_sync'):
    record.th_trigger_major(record, 'create_or_update')
        </field>
        <field name="trigger_field_ids" eval="[(6, 0, [
            ref('th_setup_parameters.field_th_major__name'),
            ref('th_setup_parameters.field_th_major__th_description'),
            ref('th_setup_parameters.field_th_major__th_major_code_aum'),
        ])]" />
        <field name="trigger">on_create_or_write</field>
        <field name="active">1</field>
    </record>

    <record id="th_odoo_trigger_major_delete" model="base.automation">
        <field name="name">Base Automation: Delete Major</field>
        <field name="model_id" ref="model_th_major"/>
        <field name="state">code</field>
        <field name="code">
if not env.context.get('th_sync'):
    record.th_trigger_major(record, 'delete')
        </field>
        <field name="trigger">on_unlink</field>
        <field name="active">1</field>
    </record>

    <record id="th_odoo_trigger_admissions_station_update" model="base.automation">
        <field name="name">Base Automation: Create or Write Admissions Station</field>
        <field name="model_id" ref="model_th_admissions_station"/>
        <field name="state">code</field>
        <field name="code">
if not env.context.get('th_sync'):
    record.th_trigger_admissions_station(record, 'create_or_update')
        </field>
        <field name="trigger_field_ids" eval="[(6, 0, [
            ref('th_setup_parameters.field_th_admissions_station__name'),
            ref('th_setup_parameters.field_th_admissions_station__th_name'),
            ref('th_setup_parameters.field_th_admissions_station__th_description'),
        ])]" />
        <field name="trigger">on_create_or_write</field>
        <field name="active">1</field>
    </record>

    <record id="th_odoo_trigger_admissions_station_delete" model="base.automation">
        <field name="name">Base Automation: Delete Admissions Station</field>
        <field name="model_id" ref="model_th_admissions_station"/>
        <field name="state">code</field>
        <field name="code">
if not env.context.get('th_sync'):
    record.th_trigger_admissions_station(record, 'delete')
        </field>
        <field name="trigger">on_unlink</field>
        <field name="active">1</field>
    </record>

    <record id="th_odoo_trigger_admissions_region_update" model="base.automation">
        <field name="name">Base Automation: Create or Write Admissions Region</field>
        <field name="model_id" ref="model_th_admissions_region"/>
        <field name="state">code</field>
        <field name="code">
if not env.context.get('th_sync'):
    record.th_trigger_admissions_region(record, 'create_or_update')
        </field>
        <field name="trigger_field_ids" eval="[(6, 0, [
            ref('th_setup_parameters.field_th_admissions_region__name'),
            ref('th_setup_parameters.field_th_admissions_region__th_description'),
        ])]" />
        <field name="trigger">on_create_or_write</field>
        <field name="active">1</field>
    </record>

    <record id="th_odoo_trigger_admissions_region_delete" model="base.automation">
        <field name="name">Base Automation: Delete Admissions Region</field>
        <field name="model_id" ref="model_th_admissions_region"/>
        <field name="state">code</field>
        <field name="code">
if not env.context.get('th_sync'):
    record.th_trigger_admissions_region(record, 'delete')
        </field>
        <field name="trigger">on_unlink</field>
        <field name="active">1</field>
    </record>

    <record id="th_odoo_trigger_status_detail_update" model="base.automation">
        <field name="name">Base Automation: Create or Write Status Detail</field>
        <field name="model_id" ref="model_th_status_detail"/>
        <field name="state">code</field>
        <field name="code">if not env.context.get('th_sync'): record.th_create_or_update_status_detail(record,'create_or_update')</field>
        <field name="trigger_field_ids" eval="[(6, 0, [
            ref('th_setup_parameters.field_th_status_detail__name'),
            ref('th_setup_parameters.field_th_status_detail__th_description'),
            ref('th_setup_parameters.field_th_status_detail__th_status_category_id'),
        ])]" />
        <field name="trigger">on_create_or_write</field>
        <field name="active">1</field>
    </record>

    <record id="th_odoo_trigger_status_detail_delete" model="base.automation">
        <field name="name">Base Automation: Delete Status Detail</field>
        <field name="model_id" ref="model_th_status_detail"/>
        <field name="state">code</field>
        <field name="code">if not env.context.get('th_sync'): record.th_create_or_update_status_detail(record,'delete')</field>
        <field name="trigger">on_unlink</field>
        <field name="active">1</field>
    </record>


    <record id="th_odoo_trigger_status_category_update" model="base.automation">
        <field name="name">Base Automation: Create or Write Status Category</field>
        <field name="model_id" ref="model_th_status_category"/>
        <field name="state">code</field>
        <field name="code">if not env.context.get('th_sync'): record.th_create_or_update_status_category(record,'create_or_update')
        </field>
        <field name="trigger_field_ids" eval="[(6, 0, [
            ref('th_setup_parameters.field_th_status_category__name'),
            ref('th_setup_parameters.field_th_status_category__th_type'),
            ref('th_setup_parameters.field_th_status_category__th_description'),
        ])]" />
        <field name="trigger">on_create_or_write</field>
        <field name="active">1</field>
    </record>

    <record id="th_odoo_trigger_status_category_delete" model="base.automation">
        <field name="name">Base Automation: Delete Status Category</field>
        <field name="model_id" ref="model_th_status_category"/>
        <field name="state">code</field>
        <field name="code">if not env.context.get('th_sync'): record.th_create_or_update_status_category(record,'delete')</field>
        <field name="trigger">on_unlink</field>
        <field name="active">1</field>
    </record>


    <record id="th_odoo_trigger_ownership_unit_update" model="base.automation">
        <field name="name">Base Automation: Create or Write Ownership Unit</field>
        <field name="model_id" ref="model_th_ownership_unit"/>
        <field name="state">code</field>
        <field name="code">
if not env.context.get('th_sync'):
    record.th_create_or_update_ownership_unit(record)
        </field>
        <field name="trigger_field_ids" eval="[(6, 0, [
            ref('th_setup_parameters.field_th_ownership_unit__name'),
            ref('th_setup_parameters.field_th_ownership_unit__th_code'),
            ref('th_setup_parameters.field_th_ownership_unit__th_is_sync'),
            ref('th_setup_parameters.field_th_ownership_unit__th_description'),
        ])]" />
        <field name="trigger">on_create_or_write</field>
        <field name="active">1</field>
    </record>

    <record id="th_odoo_trigger_ownership_unit_delete" model="base.automation">
        <field name="name">Base Automation: Delete Ownership Unit</field>
        <field name="model_id" ref="model_th_ownership_unit"/>
        <field name="state">code</field>
        <field name="code">
if not env.context.get('th_sync'):
    record.th_delete_ownership_unit(record)
        </field>
        <field name="trigger">on_unlink</field>
        <field name="active">1</field>
    </record>


    <record id="th_odoo_trigger_origin_update" model="base.automation">
        <field name="name">Base Automation: Create or Write Origin</field>
        <field name="model_id" ref="model_th_origin"/>
        <field name="state">code</field>
        <field name="code">
if not env.context.get('th_sync'):
    record.th_trigger_origin(record, 'create_or_update')
        </field>
        <field name="trigger_field_ids" eval="[(6, 0, [
            ref('th_setup_parameters.field_th_origin__name'),
            ref('th_setup_parameters.field_th_origin__th_code'),
            ref('th_setup_parameters.field_th_origin__th_module_ids'),
            ref('th_setup_parameters.field_th_origin__th_address'),
            ref('th_setup_parameters.field_th_origin__th_university_major_ids'),
            ref('th_setup_parameters.field_th_origin__th_description'),
            ref('th_sync_fastapi.field_th_origin__th_related_major'),
            ref('th_sync_fastapi.field_th_origin__th_related_subject'),
        ])]" />
        <field name="trigger">on_create_or_write</field>
        <field name="active">1</field>
    </record>

    <record id="th_odoo_trigger_origin_delete" model="base.automation">
        <field name="name">Base Automation: Delete Origin</field>
        <field name="model_id" ref="model_th_origin"/>
        <field name="state">code</field>
        <field name="code">
if not env.context.get('th_sync'):
    record.th_trigger_origin(record, 'delete')
        </field>
        <field name="trigger">on_unlink</field>
        <field name="active">1</field>
    </record>
    <record id="th_odoo_trigger_major_update" model="base.automation">
        <field name="name">Base Automation: Create or Write Major</field>
        <field name="model_id" ref="model_th_major"/>
        <field name="state">code</field>
        <field name="code">
if not env.context.get('th_sync'):
    record.th_trigger_major(record, 'create_or_update')
        </field>
        <field name="trigger_field_ids" eval="[(6, 0, [
            ref('th_setup_parameters.field_th_major__name'),
            ref('th_setup_parameters.field_th_major__th_description'),
            ref('th_setup_parameters.field_th_major__th_major_code_aum'),
        ])]" />
        <field name="trigger">on_create_or_write</field>
        <field name="active">1</field>
    </record>

    <record id="th_odoo_trigger_major_delete" model="base.automation">
        <field name="name">Base Automation: Delete Major</field>
        <field name="model_id" ref="model_th_major"/>
        <field name="state">code</field>
        <field name="code">
if not env.context.get('th_sync'):
    record.th_trigger_major(record, 'delete')
        </field>
        <field name="trigger">on_unlink</field>
        <field name="active">1</field>
    </record>

    <record id="th_odoo_trigger_admissions_station_update" model="base.automation">
        <field name="name">Base Automation: Create or Write Admissions Station</field>
        <field name="model_id" ref="model_th_admissions_station"/>
        <field name="state">code</field>
        <field name="code">
if not env.context.get('th_sync'):
    record.th_trigger_admissions_station(record, 'create_or_update')
        </field>
        <field name="trigger_field_ids" eval="[(6, 0, [
            ref('th_setup_parameters.field_th_admissions_station__name'),
            ref('th_setup_parameters.field_th_admissions_station__th_name'),
            ref('th_setup_parameters.field_th_admissions_station__th_description'),
        ])]" />
        <field name="trigger">on_create_or_write</field>
        <field name="active">1</field>
    </record>

    <record id="th_odoo_trigger_admissions_station_delete" model="base.automation">
        <field name="name">Base Automation: Delete Admissions Station</field>
        <field name="model_id" ref="model_th_admissions_station"/>
        <field name="state">code</field>
        <field name="code">
if not env.context.get('th_sync'):
    record.th_trigger_admissions_station(record, 'delete')
        </field>
        <field name="trigger">on_unlink</field>
        <field name="active">1</field>
    </record>

</odoo>
