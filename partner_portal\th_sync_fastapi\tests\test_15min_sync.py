# -*- coding: utf-8 -*-
"""
Test script để kiểm tra cơ chế đồng bộ 15 phút cho CRM Lead
"""

from odoo import api, fields, models
from odoo.tests.common import TransactionCase
from unittest.mock import patch
import logging

_logger = logging.getLogger(__name__)


class TestCrmLead15MinSync(TransactionCase):
    """Test case cho cơ chế đồng bộ 15 phút"""

    def setUp(self):
        super().setUp()
        
        # Tạo dữ liệu test
        self.th_partner = self.env['res.partner'].create({
            'name': 'Test Partner',
            'phone': '0123456789',
            'email': '<EMAIL>'
        })
        
        self.th_stage = self.env.ref('th_crm.th_stage_lead1')
        self.th_origin = self.env['th.origin'].create({
            'name': 'Test Origin'
        })
        self.th_ownership = self.env['th.ownership.unit'].create({
            'name': 'Test Ownership'
        })
        self.th_status_detail = self.env['th.status.detail'].create({
            'name': 'Test Status Detail'
        })

    def test_th_automation_quarter_hour_exists(self):
        """Kiểm tra automation quarter_hour có tồn tại và active"""
        automation = self.env.ref('th_sync_fastapi.th_odoo_trigger_crm_update_quarter_hour')
        self.assertTrue(automation.exists(), "Automation quarter_hour không tồn tại")
        self.assertTrue(automation.active, "Automation quarter_hour không được kích hoạt")
        
        # Kiểm tra các trường trigger
        th_trigger_fields = automation.trigger_field_ids.mapped('name')
        th_expected_fields = [
            'stage_id', 'th_origin_id', 'th_status_group_id', 
            'th_status_detail_id', 'th_last_check', 'th_ownership_id'
        ]
        
        for field in th_expected_fields:
            self.assertIn(field, th_trigger_fields, f"Trường {field} không có trong trigger fields")

    def test_th_automation_ontime_disabled(self):
        """Kiểm tra automation ontime đã bị vô hiệu hóa"""
        automation = self.env.ref('th_sync_fastapi.th_odoo_trigger_crm_update_ontime')
        self.assertFalse(automation.active, "Automation ontime vẫn đang được kích hoạt")

    def test_th_scheduled_action_exists(self):
        """Kiểm tra scheduled action cho quarter_hour có tồn tại"""
        cron = self.env.ref('th_sync_fastapi.th_schedule_sync_fastapi_crm_update_quarter_hour')
        self.assertTrue(cron.exists(), "Scheduled action quarter_hour không tồn tại")
        self.assertTrue(cron.active, "Scheduled action quarter_hour không được kích hoạt")
        self.assertEqual(cron.interval_number, 15, "Interval không đúng 15 phút")
        self.assertEqual(cron.interval_type, 'minutes', "Interval type không đúng")

    def test_th_clipboard_quarter_hour_creation(self):
        """Kiểm tra việc tạo clipboard với th_type_sync='quarter_hour'"""
        
        # Tạo CRM Lead
        th_lead = self.env['crm.lead'].create({
            'name': 'Test Lead',
            'partner_id': self.th_partner.id,
            'stage_id': self.th_stage.id,
            'th_origin_id': self.th_origin.id,
            'th_ownership_id': self.th_ownership.id,
        })
        
        # Thay đổi stage_id để trigger automation
        th_new_stage = self.env.ref('th_crm.th_stage_lead2')
        th_lead.write({'stage_id': th_new_stage.id})
        
        # Kiểm tra clipboard được tạo
        th_clipboard = self.env['th.clipboard'].search([
            ('th_internal_id', '=', th_lead.id),
            ('th_model_name', '=', 'crm.lead'),
            ('th_type_sync', '=', 'quarter_hour'),
            ('th_system', '=', 'samp')
        ])
        
        self.assertTrue(th_clipboard.exists(), "Clipboard quarter_hour không được tạo")
        self.assertEqual(th_clipboard.th_status, 'waiting', "Status clipboard không đúng")

    def test_th_data_filtering_quarter_hour(self):
        """Kiểm tra việc lọc dữ liệu cho quarter_hour sync"""
        
        # Tạo CRM Lead
        th_lead = self.env['crm.lead'].create({
            'name': 'Test Lead',
            'partner_id': self.th_partner.id,
            'stage_id': self.th_stage.id,
            'th_origin_id': self.th_origin.id,
            'th_ownership_id': self.th_ownership.id,
            'th_description': 'Test Description',  # Trường này không nên có trong quarter_hour
        })
        
        # Lấy dữ liệu sync cho quarter_hour
        th_data = th_lead.get_data_sync(th_lead, 'update', 'quarter_hour')
        
        # Kiểm tra chỉ có các trường quarter_hour
        th_expected_fields = [
            'stage_id', 'th_origin_id', 'th_status_group_id', 
            'th_status_detail_id', 'th_last_check', 'th_ownership_id'
        ]
        
        for field in th_expected_fields:
            if hasattr(th_lead, field) and getattr(th_lead, field):
                self.assertIn(field, th_data, f"Trường {field} không có trong dữ liệu quarter_hour")
        
        # Kiểm tra trường không thuộc quarter_hour không có
        self.assertNotIn('th_description', th_data, "Trường th_description không nên có trong quarter_hour")

    @patch('partner_portal.th_sync_fastapi.models.th_clipboard.ThClipboard.sync_fastapi')
    def test_th_scheduled_sync_execution(self, mock_sync):
        """Kiểm tra việc thực thi scheduled sync"""
        
        # Mock response thành công
        mock_sync.return_value = [{'id': 123, 'response': 'success'}]
        
        # Tạo clipboard test
        th_clipboard = self.env['th.clipboard'].create({
            'name': 'Test Sync',
            'th_model_name': 'crm.lead',
            'th_type_sync': 'quarter_hour',
            'th_internal_id': 1,
            'th_system': 'samp',
            'th_status': 'waiting',
            'th_data': {'stage_id': 1, 'th_origin_id': 2}
        })
        
        # Thực thi scheduled sync
        th_clipboard.schedule_sync_fastapi('crm.lead')
        
        # Kiểm tra API được gọi
        self.assertTrue(mock_sync.called, "API sync không được gọi")
        
        # Kiểm tra status được cập nhật
        self.assertEqual(th_clipboard.th_status, 'success', "Status không được cập nhật thành success")

    def test_th_multiple_field_changes(self):
        """Kiểm tra khi thay đổi nhiều trường cùng lúc"""
        
        # Tạo CRM Lead
        th_lead = self.env['crm.lead'].create({
            'name': 'Test Lead',
            'partner_id': self.th_partner.id,
            'stage_id': self.th_stage.id,
        })
        
        # Thay đổi nhiều trường cùng lúc
        th_new_stage = self.env.ref('th_crm.th_stage_lead2')
        th_lead.write({
            'stage_id': th_new_stage.id,
            'th_origin_id': self.th_origin.id,
            'th_ownership_id': self.th_ownership.id,
            'th_last_check': fields.Date.today()
        })
        
        # Kiểm tra chỉ tạo 1 clipboard record
        th_clipboard_count = self.env['th.clipboard'].search_count([
            ('th_internal_id', '=', th_lead.id),
            ('th_model_name', '=', 'crm.lead'),
            ('th_type_sync', '=', 'quarter_hour'),
            ('th_system', '=', 'samp')
        ])
        
        self.assertEqual(th_clipboard_count, 1, "Nên chỉ tạo 1 clipboard record cho nhiều thay đổi")

    def test_th_clipboard_selection_field(self):
        """Kiểm tra trường th_type_sync có option quarter_hour"""
        th_clipboard_model = self.env['th.clipboard']
        th_selection_values = dict(th_clipboard_model._fields['th_type_sync'].selection)
        
        self.assertIn('quarter_hour', th_selection_values, "Option quarter_hour không có trong selection")
        self.assertEqual(th_selection_values['quarter_hour'], '15 phút 1 lần', "Label quarter_hour không đúng")

    def tearDown(self):
        """Cleanup sau test"""
        # Xóa dữ liệu test
        self.env['th.clipboard'].search([
            ('th_model_name', '=', 'crm.lead'),
            ('th_type_sync', '=', 'quarter_hour')
        ]).unlink()
        
        super().tearDown()


class TestCrmLeadSyncPerformance(TransactionCase):
    """Test performance cho đồng bộ 15 phút"""

    def test_th_batch_sync_performance(self):
        """Kiểm tra hiệu suất khi đồng bộ nhiều bản ghi"""
        
        # Tạo nhiều clipboard records
        th_clipboard_data = []
        for i in range(100):
            th_clipboard_data.append({
                'name': f'Test Sync {i}',
                'th_model_name': 'crm.lead',
                'th_type_sync': 'quarter_hour',
                'th_internal_id': i + 1,
                'th_system': 'samp',
                'th_status': 'waiting',
                'th_data': {'stage_id': 1, 'th_origin_id': 2}
            })
        
        th_clipboards = self.env['th.clipboard'].create(th_clipboard_data)
        
        # Đo thời gian thực thi
        import time
        th_start_time = time.time()
        
        with patch('partner_portal.th_sync_fastapi.models.th_clipboard.ThClipboard.sync_fastapi') as mock_sync:
            mock_sync.return_value = [{'id': i, 'response': 'success'} for i in range(100)]
            th_clipboards.schedule_sync_fastapi('crm.lead')
        
        th_end_time = time.time()
        th_execution_time = th_end_time - th_start_time
        
        # Kiểm tra thời gian thực thi hợp lý (< 5 giây cho 100 records)
        self.assertLess(th_execution_time, 5.0, f"Thời gian thực thi quá lâu: {th_execution_time}s")
        
        _logger.info(f"Batch sync 100 records took: {th_execution_time:.2f}s")
