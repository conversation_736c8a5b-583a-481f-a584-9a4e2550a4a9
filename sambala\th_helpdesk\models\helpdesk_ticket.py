from dateutil.relativedelta import relativedelta
from odoo import models, fields, _, api

from pytz import timezone, UTC
from datetime import datetime
import json
from collections import defaultdict

from odoo.exceptions import ValidationError


class HelpdeskTicket(models.Model):
    _inherit = 'helpdesk.ticket'
    _order = "create_date desc"

    x_rating_ids = fields.One2many('x.helpdesk.rating', 'x_helpdesk_ticket_id', 'Ticket rating')
    partner_id = fields.Many2one(default=lambda self: self.env.user.partner_id.id)
    x_actions = fields.Html(string="Action")
    x_suggestion = fields.Html(string='Suggestion')
    x_is_ticket_seeker = fields.Boolean(compute='compute_x_is_ticket_seeker')
    x_is_done_ticket = fields.Boolean('Is Done ticket', related='stage_id.x_is_done_stage')
    # x_is_closed_ticket = fields.Boolean('Is Closed ticket', related='stage_id.is_close')
    x_is_new_ticket = fields.Boolean('Is New ticket', related='stage_id.x_is_new_stage')
    x_ticket_rating = fields.Selection([('1', 'Excellent'),
                                        ('2', 'Good'),
                                        ('3', 'Satisfactory'),
                                        ('4', 'Not good'),
                                        ('5', 'Not Satisfied')],
                                       compute='compute_x_ticket_rating', store=True, string='Satisfied rating')
    th_ticket_stage = fields.Selection(string='Ticket Stage', related="stage_id.th_ticket_stage")
    th_type_ticket = fields.Selection(selection=[('external', 'External Help Ticket'), ('internal', 'Internal Help Ticket')], string="Help Ticket Type")
    th_support_area_id = fields.Many2one(comodel_name="th.support.area",
                                         tracking=True,
                                         string="Khu vực hỗ trợ")
    th_help_category = fields.Many2one(comodel_name="th.helpdesk.category",
                                       string="Help Category",
                                       tracking=True,
                                       domain="[('th_support_area_id', '=?', th_support_area_id)]")
    th_category_characteristic_id = fields.Many2one(comodel_name="th.category.characteristic",
                                                    string="Đặc điểm danh mục",
                                                    tracking=True,
                                                    domain="[('th_helpdesk_category_id', '=?', th_help_category)]")
    th_help_topic = fields.Many2one(comodel_name="th.help.topic",
                                    string="Help Topic",
                                    tracking=True,
                                    domain="[('th_category_characteristic_id', '=?', th_category_characteristic_id)]")

    team_id = fields.Many2one('helpdesk.team', string='Helpdesk Team', default=False)
    user_id = fields.Many2one(
        'res.users', string='Assigned to', compute='_compute_user_and_stage_ids_th', store=True,
        readonly=True, tracking=True,
        domain=lambda self: [('groups_id', 'in', self.env.ref('helpdesk.group_helpdesk_user').id)])
    stage_id = fields.Many2one('helpdesk.stage', domain=[])
    th_user_id = fields.Many2one('res.users', string='Assigned to', tracking=True)
    th_user_id_domain = fields.Char(compute="_compute_assigned_user")

    th_transferred_ids = fields.One2many(comodel_name="th.transferred", inverse_name="th_ticket_id", domain=[('th_transfer', '=', False)], string="Transferred")
    th_escalate_ids = fields.One2many(comodel_name="th.transferred", inverse_name="th_ticket_id", domain=[('th_transfer', '=', True)] ,string="Escalate")
    th_sla_deadline_ids = fields.Many2many(comodel_name="th.sla.deadline", string="SLA Deadline")
    th_sla_deadline = fields.Datetime("SLA Deadline", compute='_compute_th_sla_deadline', compute_sudo=True, store=True, help="The closest deadline of all SLA applied on this ticket")
    th_in_progress_hours = fields.Integer(string="Execution time (hours)", compute="compute_th_in_progress_hours", store=True)
    th_solved_date = fields.Datetime(string="Solved date")
    th_in_progress_date = fields.Datetime(string="Start date of the process")
    th_help_provider_id = fields.Many2one(comodel_name="th.help.provider.units", string="Help Provider Units")
    th_attachment_ids = fields.Many2many(comodel_name="ir.attachment", string="Attachment")
    th_check_groups = fields.Boolean(string="Groups", compute="compute_th_check_groups")
    th_deadline_user = fields.Datetime(string="Customer deadline", tracking=True)
    th_deadline_assignees = fields.Datetime(string="Assignee deadline", tracking=True)
    th_is_deadline_user = fields.Boolean(compute='compute_th_is_deadline_user', string='🛒')
    th_is_deadline_assignees = fields.Boolean(compute='compute_th_is_deadline_assignees', string='🛒')
    th_check_user_assign_to = fields.Boolean(compute='compute_th_check_user_assign_to', string='🛒')
    th_categ_of_tthl = fields.Boolean(string="Đánh dấu là của trung tâm học liệu", related="th_help_category.th_categ_of_tthl", store=True)
    th_type_errol = fields.Selection(
        selection=[('expertise', 'Chuyên môn'), ('technique', 'Kỹ thuật'), ('system', 'Hệ thống')],
        string="Phân loại lỗi")
    th_composite = fields.Selection(
        selection=[('outline', 'Đề cương chi tiết'), ('quest', 'Ngân hàng câu hỏi'), ('text', 'Text'), ('record', 'Thu âm')],
        string="Thành phần")
    th_major_id = fields.Many2one(comodel_name="th.major", string="Ngành")
    th_user_production_id = fields.Many2one("res.users", string="Phụ trách sản xuất")
    th_check_helpdesk_normal_user = fields.Boolean(string="Check nhóm quyền nhân viên Helpdesk",
                                                   compute="_compute_th_check_helpdesk_normal_user")
    th_is_maintenance = fields.Boolean(default=False, compute="_compute_th_is_maintenance")
    th_is_user_ticket = fields.Boolean(default=False, compute="_compute_th_is_user_ticket")
    th_requests_id = fields.Many2one(
        'th.fix.request',
        string="Phiếu sữa chữa", ondelete="cascade"
    )
    
    @api.depends('th_user_id')
    def _compute_th_is_maintenance(self):
        for rec in self:
            if (rec.th_user_id and rec.th_user_id == self.env.user) or self.user_has_groups('th_helpdesk.group_helpdesk_care_manager'):
                rec.th_is_maintenance = True
            else:
                rec.th_is_maintenance = False

    @api.depends('create_uid')
    def _compute_th_is_user_ticket(self):
        for rec in self:
            is_care_manager = self.user_has_groups('th_helpdesk.group_helpdesk_care_manager')
            is_creator = rec.create_uid and rec.create_uid == self.env.user
            rec.th_is_user_ticket =  is_care_manager or is_creator
    
    def change_to_maintenace(self):
        self.ensure_one()
        fix_request = self.env['th.fix.request'].create({
            'name' : self.name,
            'th_partner_id' : self.partner_id.id,
            'th_estimated_deadline': self.th_deadline_user,
            'th_attachment_ids' : self.th_attachment_ids.id,
            'th_priority' : self.priority,
            'th_reason' : self.description
        })
        self.th_requests_id = fix_request.id

    def check_team_id_check_user(self, name_topic):
        if self.team_id and not self.team_id.member_ids:
            raise ValidationError(f'Chủ đề hỗ trợ "{name_topic}" hiện tại chưa có người phụ trách! Vui lòng liên hệ với ADMIN hệ thống để được giải quyết')

    @api.onchange('team_id',)
    def onchange_team_id_check_user(self):
        if self.team_id and not self.team_id.member_ids:
            raise ValidationError(f'Đội hỗ trợ {self.team_id.name} hiện tại chưa có người phụ trách! Vui lòng liên hệ với ADMIN hệ thống để được giải quyết')

    @api.depends('th_user_id')
    def compute_th_check_user_assign_to(self):
        for rec in self:
            rec.th_check_user_assign_to = True if rec.th_user_id and rec.th_user_id == self.env.user else False

    @api.depends('stage_id')
    def compute_th_is_stage_test(self):
        for rec in self:
            stage_id = self.env['helpdesk.stage'].search([])

    @api.depends('partner_id')
    def compute_th_is_deadline_user(self):
        for rec in self:
            rec.th_is_deadline_user = True if (rec.partner_id and rec.partner_id == self.env.user.partner_id) or self.user_has_groups('helpdesk.group_helpdesk_manager') else False

    @api.depends('th_user_id', 'team_id')
    def compute_th_is_deadline_assignees(self):
        for rec in self:
            rec.th_is_deadline_assignees = True if (rec.th_user_id and rec.th_user_id == self.env.user) or self.user_has_groups('helpdesk.group_helpdesk_manager') or ( rec.team_id and rec.team_id.th_administrator == self.env.user and self.user_has_groups('th_helpdesk.group_helpdesk_care_manager')) else False

    @api.depends('team_id')
    def _compute_assigned_user(self):
        for rec in self:
            domain = []
            if rec.team_id:
                domain.append(['id', 'in', rec.team_id.member_ids.ids])
            else:
                domain.append(['id', '=', []])
            rec.th_user_id_domain = json.dumps(domain)

    @api.depends('partner_id', 'team_id')
    def compute_th_check_groups(self):
        for rec in self:
            rec.th_check_groups = True if self.user_has_groups('helpdesk.group_helpdesk_manager') or (rec.team_id and rec.team_id.th_administrator == self.env.user)  else False

    def compute_th_in_progress_hours(self):
        for rec in self:
            th_in_progress_hours = 0
            if rec.th_solved_date and rec.th_in_progress_date:
                th_in_progress_hours = (rec.th_solved_date - rec.th_in_progress_date).days * 8 + int((rec.th_solved_date - rec.th_in_progress_date).seconds /3600)
                sundays = [rec.th_in_progress_date + relativedelta(days=x) for x in range((rec.th_solved_date - rec.th_in_progress_date).days + 1) if (rec.th_in_progress_date + relativedelta(days=x)).weekday() == 6]
                saturday = [rec.th_in_progress_date + relativedelta(days=x) for x in range((rec.th_solved_date - rec.th_in_progress_date).days + 1) if (rec.th_in_progress_date + relativedelta(days=x)).weekday() == 5]
            rec.th_in_progress_hours = th_in_progress_hours - len(sundays) * 8 - len(saturday) * 4 if th_in_progress_hours > 0 else 0

    @api.depends('th_sla_deadline_ids')
    def _compute_th_sla_deadline(self):
        for rec in self:
            th_sla_deadline = False
            if rec.create_date:
                # end_date = rec.create_date + relativedelta(hour=10, minute=0, second=0)
                create_date = UTC.localize(rec.create_date).astimezone(timezone(self.env.user.tz or 'Asia/Ho_Chi_Minh')).replace(tzinfo=None) if rec.create_date else False
                end_date = UTC.localize(rec.create_date + relativedelta(hour=10, minute=0, second=0)).astimezone(timezone(self.env.user.tz or 'Asia/Ho_Chi_Minh')).replace(tzinfo=None) if rec.create_date else False
                total_hours = sum(rec.th_sla_deadline_ids.mapped('th_hour'))
                if (end_date - create_date).seconds > 0 and (end_date - create_date).days > 0:
                    time = (end_date - create_date).seconds / 3600
                    hour = total_hours - time
                    days = int(hour // 8)
                    sundays = [create_date + relativedelta(days=x) for x in range(days + 1) if (create_date + relativedelta(days=x)).weekday() == 6]
                    saturday = [create_date + relativedelta(days=x) for x in range(days + 1) if (create_date + relativedelta(days=x)).weekday() == 5]
                    day_number = days + len(sundays) + len(saturday) if hour - (days*8) - (len(saturday)*4) < 0 else days + len(sundays) + len(saturday) +1
                    th_sla_deadline = create_date + relativedelta(days=day_number)
                    if th_sla_deadline.weekday() == 5 and (hour - (days*8) - (len(saturday)*4))>4:
                        th_sla_deadline += relativedelta(days=1)
                else:
                    days = int(total_hours // 8)
                    sundays = [create_date + relativedelta(days=x) for x in range(days + 1) if (create_date + relativedelta(days=x)).weekday() == 6]
                    saturday = [create_date + relativedelta(days=x) for x in range(days + 1) if (create_date + relativedelta(days=x)).weekday() == 5]
                    day_number = days + len(sundays) + len(saturday) if total_hours - (days*8) - (len(saturday)*4) < 0 else days + len(sundays) + len(saturday) +1
                    th_sla_deadline = create_date + relativedelta(days=day_number )
                    if th_sla_deadline.weekday() == 5 and (total_hours - (days*8) - (len(saturday)*4))>4:
                        th_sla_deadline += relativedelta(days=1 )
                if th_sla_deadline.weekday() == 6:
                    th_sla_deadline += relativedelta(days=1 )
            rec.th_sla_deadline = UTC.localize(th_sla_deadline + relativedelta(hours=-14)).astimezone(timezone(self.env.user.tz or 'Asia/Ho_Chi_Minh')).replace(tzinfo=None) if th_sla_deadline else False

    @api.depends('x_rating_ids')
    def compute_x_ticket_rating(self):
        for rec in self:
            rec.x_ticket_rating = rec.x_rating_ids[0].x_rating if rec.x_rating_ids else False

    @api.depends('team_id', 'th_user_id')
    def _compute_user_and_stage_ids_th(self):
        for ticket in self.filtered(lambda ticket: ticket.team_id):
            if ticket.team_id and ticket.th_user_id:
                ticket.user_id = ticket.th_user_id.id
            if not ticket.stage_id or ticket.stage_id not in ticket.team_id.stage_ids:
                ticket.stage_id = ticket.team_id._determine_stage()[ticket.team_id.id]

    @api.onchange('th_category_characteristic_id', 'th_type_ticket', 'th_help_provider_id')
    def onchange_th_type_ticket_and_th_help_category(self):
        if self.th_type_ticket and self.th_type_ticket == 'internal' and self.th_category_characteristic_id:
            return {'domain': {'th_help_topic': [('th_category_characteristic_id', '=', self.th_category_characteristic_id.id), ('th_helpdesk_team.th_type_ticket', '=', self.th_type_ticket)]}}
        elif self.th_type_ticket and self.th_type_ticket == 'external' and self.th_help_provider_id and self.th_category_characteristic_id:
            return {'domain': {'th_help_topic': [('th_category_characteristic_id', '=', self.th_category_characteristic_id.id), ('th_helpdesk_team.th_type_ticket', '=', self.th_type_ticket), ('th_helpdesk_team', '=', self.th_help_provider_id.th_helpdesk_team.id)]}}
        elif self.th_type_ticket and self.th_type_ticket == 'external' and not self.th_help_provider_id and self.th_category_characteristic_id:
            return {'domain': {'th_help_topic': [('th_category_characteristic_id', '=', self.th_category_characteristic_id.id), ('th_helpdesk_team.th_type_ticket', '=', self.th_type_ticket)]}}
        elif not self.th_type_ticket and self.th_category_characteristic_id:
            return {'domain': {'th_help_topic': [('th_category_characteristic_id', '=', self.th_category_characteristic_id.id)]}}
        else:
            return {'domain': {'th_help_topic': []}}

    @api.onchange('th_help_provider_id')
    def onchange_th_help_provider_id(self):
        if self.th_help_topic and self.th_help_topic.th_helpdesk_team != self.th_help_provider_id.th_helpdesk_team:
            self.th_help_topic = False
            self.th_user_id = False

    @api.onchange('th_help_topic', 'th_help_provider_id')
    def onchange_th_topic_th(self):
        if not self.th_category_characteristic_id:
            self.th_category_characteristic_id = self.th_help_topic.th_category_characteristic_id.id
        if not self.th_type_ticket:
            self.th_type_ticket = self.th_help_topic.th_helpdesk_team.th_type_ticket
        if self.th_type_ticket == 'internal':
            self.team_id = self.th_help_topic.th_helpdesk_team.id if self.th_help_topic else False
        if self.th_type_ticket == 'external':
            self.team_id = self.th_help_provider_id.th_helpdesk_team.id if self.th_help_provider_id and self.th_help_topic and self.th_help_topic.th_helpdesk_team == self.th_help_provider_id.th_helpdesk_team else False
        if self.team_id and self.th_help_topic and self.th_help_topic.th_helpdesk_team:
            self.check_team_id_check_user(self.th_help_topic.name)
            self.th_user_id = self.team_id._determine_user_to_assign()[self.team_id.id]

    @api.onchange('th_category_characteristic_id')
    def onchange_th_help_topic(self):
        if self.th_category_characteristic_id and self.th_help_topic.th_category_characteristic_id != self.th_category_characteristic_id:
            self.th_help_topic = False
            self.team_id = False
            self.th_user_id = False

    @api.onchange('th_type_ticket')
    def onchange_th_type_ticket(self):
        if self.th_type_ticket and self.team_id and self.team_id.th_type_ticket != self.th_type_ticket:
            self.th_help_topic = False
            self.th_user_id = False
            self.team_id = False

    def button_add_rating(self):
        self.ensure_one()
        if self.env['x.helpdesk.rating'].search([('x_helpdesk_ticket_id', '=', self.id)], order="id desc", limit=1):
            self.env['x.helpdesk.rating'].search([('x_helpdesk_ticket_id', '=', self.id)], order="id desc", limit=1).write({'th_readonly': False})
            return {
                'name': _('Are you satisfied with this support?'),
                'view_mode': 'form',
                'res_model': 'x.helpdesk.rating',
                'views': [[False, "form"]],
                'res_id': self.env['x.helpdesk.rating'].search([('x_helpdesk_ticket_id', '=', self.id)], order="id desc", limit=1).id,
                'type': 'ir.actions.act_window',
                'context': {'wizard': 1,'default_x_helpdesk_ticket_id': self.id, 'create': False, 'edit': True},
                'target': 'new',
            }
        else:
            return {
                'name': _('Are you satisfied with this support?'),
                'view_mode': 'form',
                'res_model': 'x.helpdesk.rating',
                'views': [[False, "form"]],
                'type': 'ir.actions.act_window',
                'context': {'wizard': 1, 'default_x_helpdesk_ticket_id': self.id, 'create': False},
                'target': 'new',
            }

    def button_add_transferring(self):
        self.ensure_one()
        return {
            'name': _('Transfer reason'),
            'view_mode': 'form',
            'res_model': 'th.transferred',
            'views': [[False, "form"]],
            'type': 'ir.actions.act_window',
            'context': {'wizard': 1,
                        'default_th_ticket_id': self.id,
                        # 'default_th_type_ticket': self.th_type_ticket,
                        'default_th_support_area_id': self.th_support_area_id.id,
                        'default_th_help_category': self.th_help_category.id,
                        'default_th_category_characteristic_id': self.th_category_characteristic_id.id,
                        'default_th_help_topic': self.th_help_topic.id,
                        'default_th_transfer_to_id': self.th_user_id.id,
                        'default_th_user_groups': self.user_has_groups('helpdesk.group_helpdesk_manager') or ( self.team_id and self.team_id.th_administrator == self.env.user and self.user_has_groups('th_helpdesk.group_helpdesk_care_manager'))},
            'target': 'new',
        }

    def action_view_ticket_rating(self):
        self.ensure_one()
        self.x_rating_ids[0].write({'th_readonly': True})
        return {
            'name': _('Rating helpdesk ticket'),
            'view_mode': 'form',
            'res_model': 'x.helpdesk.rating',
            'views': [[False, "form"]],
            'type': 'ir.actions.act_window',
            'res_id': self.x_rating_ids[0].id,
            'target': 'new',
            'context': {'wizard': 0, 'create': False, 'delete': False, 'edit':False},
        }

    def compute_x_is_ticket_seeker(self):
        for rec in self:
            rec.x_is_ticket_seeker = True if rec.partner_id == self.env.user.partner_id else False

    # @api.model
    # def create(self, values):
    #     res = super(HelpdeskTicket, self).create(values)
    #     deadline = self.env['th.sla.deadline'].search([('team_id', 'in', [False, res.team_id.id]), ('th_type_ticket', 'in', [False, res.th_type_ticket]),'|', ('th_help_category', '=', False), ('th_help_category', '=',res.th_help_category.id), '|', ('th_help_topic', '=', False), ('th_help_topic', '=', res.th_help_topic.id)])
    #     res.write(dict(th_sla_deadline_ids=[(6, 0, deadline.ids)]))
    #     for rec in res:
    #         for att in rec.th_attachment_ids:
    #             att.res_id = rec.id
    #     # res.onchange_th_topic_th()
    #     return res

    @api.model_create_multi
    def create(self, values_list):
        res = super().create(values_list)

        for rec in res:
            deadline = self.env['th.sla.deadline'].search([
                ('team_id', 'in', [False, rec.team_id.id]),
                ('th_type_ticket', 'in', [False, rec.th_type_ticket]),
                '|', ('th_help_category', '=', False), ('th_help_category', '=', rec.th_help_category.id),
                '|', ('th_help_topic', '=', False), ('th_help_topic', '=', rec.th_help_topic.id)
            ])
            rec.write({'th_sla_deadline_ids': [(6, 0, deadline.ids)]})

            for att in rec.th_attachment_ids:
                att.res_id = rec.id

        return res
    
    # thông báo cho th_user_id khi được giao ticket mới
    @api.constrains('th_user_id')
    def _notify_th_user_id(self):
        for ticket in self:
            if ticket.th_user_id:
                # Tạo thông báo cho người được phân công
                ticket.activity_schedule(
                    activity_type_id=self.env.ref('mail.mail_activity_data_meeting').id, 
                    summary=_('Xử lý ticket: %s', ticket.name),
                    note=_('Bạn đã được phân công xử lý ticket này'),
                    user_id=ticket.th_user_id.id,
                )
            
    def _schedule_escalate_sla_th(self):
        helpdesk = self.env['helpdesk.ticket'].search([('th_sla_deadline', '!=', False)])
        ticket = helpdesk.filtered(lambda h: datetime.strptime(h.th_sla_deadline.strftime('%Y-%m-%d'), '%Y-%m-%d').date() == fields.Date.today())
        for rec in ticket:
            vals = {'th_reason': _('Tickets have expired SLA'),
                    'th_ticket_id': rec.id,
                    'th_transfer': True}
            user = self.env['res.users']
            if not rec.th_escalate_ids:
                user |= rec.team_id.th_administrator
            else:
                user |= rec.th_user_id.employee_id.parent_id.user_id
            vals['th_transfer_to_id'] = user.id
            team_id = self.env['helpdesk.team'].search(['|', ('th_administrator', '=', user.id), ('member_ids', 'in', user.id)])
            if team_id:
                vals['th_team_id'] = team_id.id
            else:
                vals['th_team_id'] = False
            rec.write({'th_user_id': user.id, 'team_id': team_id.id})
            self.env['th.transferred'].create(vals)

    def write(self, values):
        if 'description' in values:
            log_note = _("Mô tả thay đổi: %(old_value)s --> %(new_value)s", old_value=self.description,
                         new_value=values.get('description'))
            self.message_post(body=log_note)
        res = super(HelpdeskTicket, self).write(values)
        if 'stage_id' in values:
            for rec in self:
                if rec.stage_id.th_ticket_stage == 'in_progress':
                    rec.th_in_progress_date = fields.Datetime.now()
                if rec.stage_id.th_ticket_stage == 'solved':
                    rec.th_solved_date = fields.Datetime.now()
        if 'team_id' in values:
            for rec in self:
                deadline = self.env['th.sla.deadline'].search([('team_id', 'in', [False, rec.team_id.id]), ('th_type_ticket', 'in', [False, rec.th_type_ticket]), '|', ('th_help_category', '=', False), ('th_help_category', '=', rec.th_help_category.id), '|', ('th_help_topic', '=', False), ('th_help_topic', '=', rec.th_help_topic.id)])
                rec.write(dict(th_sla_deadline_ids=[(6, 0, deadline.ids)]))
        if 'th_user_id' in values:
            for rec in self:
                rec.sudo()._message_auto_subscribe_notify(rec.th_user_id.partner_id.ids, 'mail.message_user_assigned')
        return res

    def _compute_th_check_helpdesk_normal_user(self):
        for rec in self:
            rec.th_check_helpdesk_normal_user = self.env.user.has_group('th_helpdesk.group_helpdesk_normal_user')
            if self.env.user.has_group('helpdesk.group_helpdesk_user'):
                rec.th_check_helpdesk_normal_user = False

class ResGroup(models.Model):
    _inherit = 'res.groups'

    @api.model
    def get_groups_by_application(self):
        """ Return all groups classified by application (module category), as a list::

                [(app, kind, groups), ...],

            where ``app`` and ``groups`` are recordsets, and ``kind`` is either
            ``'boolean'`` or ``'selection'``. Applications are given in sequence
            order.  If ``kind`` is ``'selection'``, ``groups`` are given in
            reverse implication order.
        """

        def linearize(app, gs, category_name):
            # 'User Type' is an exception
            if app.xml_id == 'base.module_category_user_type':
                return (app, 'selection', gs.sorted('id'), category_name)
            # determine sequence order: a group appears after its implied groups
            order = {g: len(g.trans_implied_ids & gs) for g in gs}
            # We want a selection for Accounting too. Auditor and Invoice are both
            # children of Accountant, but the two of them make a full accountant
            # so it makes no sense to have checkboxes.
            if app.xml_id == 'base.module_category_accounting_accounting':
                return (app, 'selection', gs.sorted(key=order.get), category_name)

            # gộp quyền module helpdesk
            if app.xml_id == 'base.module_category_services_helpdesk':
                return (app, 'selection', gs.sorted(key=order.get), category_name)

            # check whether order is total, i.e., sequence orders are distinct
            if len(set(order.values())) == len(gs):
                return (app, 'selection', gs.sorted(key=order.get), category_name)
            else:
                return (app, 'boolean', gs, (100, 'Other'))

        # classify all groups by application
        by_app, others = defaultdict(self.browse), self.browse()
        for g in self.get_application_groups([]):
            if g.category_id:
                by_app[g.category_id] += g
            else:
                others += g
        # build the result
        res = []
        for app, gs in sorted(by_app.items(), key=lambda it: it[0].sequence or 0):
            if app.parent_id:
                res.append(linearize(app, gs, (app.parent_id.sequence, app.parent_id.name)))
            else:
                res.append(linearize(app, gs, (100, 'Other')))

        if others:
            res.append((self.env['ir.module.category'], 'boolean', others, (100, 'Other')))
        return res