from odoo import api, fields, models
from ..sync_helper_one_way import THSyncHelper
import logging

_logger = logging.getLogger(__name__)

class THSourceGroup(models.Model):
    _name = "th.source.group"
    _inherit = ["th.source.group", "th.intermediate.table"]

    def get_data_sync(self, rec):
        data = {
            'name': rec.name if rec.name else False,
            'th_description': rec.th_description if rec.th_description else False,
        }
        return data, data.copy()

    @api.model
    def th_trigger_source_group(self, records, action):
        for rec in records:
            th_data, th_data_send = self.get_data_sync(rec)
            val = {
                'name': rec.name,
                'th_type_sync': 'ontime',
                'th_internal_id': rec.id,
                'th_data': th_data,
                'th_system': 'b2b',
            }
            if action == 'create_or_update':
                val['th_data_send'] = th_data_send
                self.sudo().th_func_create_or_update(val)
            elif action == 'delete':
                self.sudo().th_func_delete(val)

class ThGraduationSystem(models.Model):
    _name = "th.graduation.system"
    _inherit = ["th.graduation.system", "th.intermediate.table"]

    def get_data_sync(self, rec):
        data = {
            'name': rec.name if rec.name else False,
            'th_description': rec.th_description if rec.th_description else False,
        }
        return data, data.copy()

    @api.model
    def th_trigger_graduation_system(self, records, action, th_type_sync='ontime'):
        for rec in records:
            th_data, th_data_send = self.get_data_sync(rec)
            val = {
                'name': rec.name,
                'th_type_sync': th_type_sync,
                'th_internal_id': rec.id,
                'th_data': th_data,
                'th_system': 'b2b',
            }
            if action == 'create_or_update':
                val['th_data_send'] = th_data_send
                self.sudo().th_func_create_or_update(val)
            elif action == 'delete':
                self.sudo().th_func_delete(val)


class ThMajor(models.Model):
    _name = "th.major"
    _inherit = ["th.major", "th.intermediate.table"]

    def get_data_sync(self, rec):
        data = {
            'name': rec.name if rec.name else False,
            'th_description': rec.th_description if rec.th_description else False,
            'th_major_code_aum': rec.th_major_code_aum if rec.th_major_code_aum else False,
        }
        return data, data.copy()

    @api.model
    def th_trigger_major(self, records, action, th_type_sync='ontime'):
        for rec in records:
            th_data, th_data_send = self.get_data_sync(rec)
            val = {
                'name': rec.name,
                'th_type_sync': th_type_sync,
                'th_internal_id': rec.id,
                'th_data': th_data,
                'th_system': 'b2b',
            }
            if action == 'create_or_update':
                val['th_data_send'] = th_data_send
                self.sudo().th_func_create_or_update(val)
            elif action == 'delete':
                self.sudo().th_func_delete(val)


class ThAdmissionsStation(models.Model):
    _name = "th.admissions.station"
    _inherit = ["th.admissions.station", "th.intermediate.table"]

    def get_data_sync(self, rec):
        data = {
            'name': rec.name if rec.name else False,
            'th_name': rec.th_name if rec.th_name else False,
            'th_description': rec.th_description if rec.th_description else False,
        }
        return data, data.copy()

    @api.model
    def th_trigger_admissions_station(self, records, action, th_type_sync='ontime'):
        for rec in records:
            th_data, th_data_send = self.get_data_sync(rec)
            val = {
                'name': rec.name,
                'th_type_sync': th_type_sync,
                'th_internal_id': rec.id,
                'th_data': th_data,
                'th_system': 'b2b',
            }
            if action == 'create_or_update':
                val['th_data_send'] = th_data_send
                self.sudo().th_func_create_or_update(val)
            elif action == 'delete':
                self.sudo().th_func_delete(val)


class ThAdmissionsRegion(models.Model):
    _name = "th.admissions.region"
    _inherit = ["th.admissions.station", "th.intermediate.table"]

    def get_data_sync(self, rec):
        data = {
            'name': rec.name if rec.name else False,
            'th_description': rec.th_description if rec.th_description else False,
        }
        return data, data.copy()

    @api.model
    def th_trigger_admissions_region(self, records, action, th_type_sync='ontime'):
        for rec in records:
            th_data, th_data_send = self.get_data_sync(rec)
            val = {
                'name': rec.name,
                'th_type_sync': th_type_sync,
                'th_internal_id': rec.id,
                'th_data': th_data,
                'th_system': 'b2b',
            }
            if action == 'create_or_update':
                val['th_data_send'] = th_data_send
                self.sudo().th_func_create_or_update(val)
            elif action == 'delete':
                self.sudo().th_func_delete(val)


class ThTrainingSystem(models.Model):
    _name = "th.training.system"
    _inherit = ["th.training.system", "th.intermediate.table"]

    def get_data_sync(self, rec):
        data = {
            'name': rec.name if rec.name else False,
            'th_description': rec.th_description if rec.th_description else False,
        }
        return data, data.copy()

    @api.model
    def th_trigger_training_system(self, records, action, th_type_sync='ontime'):
        for rec in records:
            th_data, th_data_send = self.get_data_sync(rec)
            val = {
                'name': rec.name,
                'th_type_sync': th_type_sync,
                'th_internal_id': rec.id,
                'th_data': th_data,
                'th_system': 'b2b',
            }
            if action == 'create_or_update':
                val['th_data_send'] = th_data_send
                self.sudo().th_func_create_or_update(val)
            elif action == 'delete':
                self.sudo().th_func_delete(val)


class ThOrigin(models.Model):
    _name = "th.origin"
    _inherit = ["th.origin", "th.intermediate.table"]

    def get_data_sync(self, rec, is_mapping=True):
        data = {
            'name': rec.name if rec.name else False,
            'th_code': rec.th_code if rec.th_code else False,
            'th_module_ids': rec.th_module_ids.ids if rec.th_module_ids else False,
            'th_address': rec.th_address if rec.th_address else False,
            'th_university_major_ids': rec.th_university_major_ids.ids if rec.th_university_major_ids else False,
            'th_description': rec.th_description if rec.th_description else False,
        }
        data_send = data.copy()
        data_send.update({
            'th_module_ids': self.sudo()._find_external_id(th_internal_ids=rec.th_module_ids.ids,
                                                           th_model_name='therp.module') if rec.th_module_ids else False,
        })
        data_send['th_university_major_ids'] = []
        for i in rec.th_university_major_ids:
            val = {
                'th_major_code_university': i.th_major_code_university if i.th_major_code_university else False,
                'th_subject': self.sudo()._find_external_id(th_internal_ids=i.th_subject.ids,
                                                           th_model_name='th.subject') if i.th_subject else False,
                'th_major_id': self.sudo()._find_external_id(th_internal_ids=i.th_major_id.ids,
                                                             th_model_name='th.major')[0]
                if i.th_major_id and self.sudo()._find_external_id(th_internal_ids=i.th_major_id.ids, th_model_name='th.major') else False,
            }
            data_send['th_university_major_ids'].append(val)
        return data, data_send

    @api.model
    def th_trigger_origin(self, records, action):
        for rec in records:
            th_data, th_data_send = self.get_data_sync(rec)
            val = {
                'name': rec.name,
                'th_type_sync': 'ontime',
                'th_internal_id': rec.id,
                'th_data': th_data,
                'th_system': 'b2b',
            }
            if action == 'create_or_update':
                val['th_data_send'] = th_data_send
                self.sudo().th_func_create_or_update(val)
            elif action == 'delete':
                self.sudo().th_func_delete(val)


class ThStatusDetail(models.Model):
    _name = "th.status.detail"
    _inherit = ["th.status.detail", "th.intermediate.table"]

    def get_data_sync(self, rec, is_mapping=True):
        data = {
            'name': rec.name if rec.name else False,
            'th_description': rec.th_description if rec.th_description else False,
            'th_status_category_id': rec.th_status_category_id.id if rec.th_status_category_id else False,
        }
        th_data_send = {
            'name': rec.name if rec.name else False,
            'th_description': rec.th_description if rec.th_description else False,
            'th_status_category_id': self.sudo()._find_external_id(th_internal_id=rec.th_status_category_id.id,
                                                            th_model_name='th.status.category') if rec.th_status_category_id else False,
        } if is_mapping else None
        return data, th_data_send

    @api.model
    def th_create_or_update_status_detail(self, status_detail_data, action):
        _logger.info(f"Create_or_Write Trigger for Status Detail: {status_detail_data.id}")
        for rec in status_detail_data:
            th_data, th_data_send = self.get_data_sync(rec)
            val = {
                'name': rec.name,
                'th_type_sync': 'ontime',
                'th_internal_id': rec.id,
                'th_data': th_data,
                'th_data_send': th_data_send,
                'th_module_id': False,
                'th_system': 'b2b',
            }
            if action == 'create_or_update':
                val['th_data_send'] = th_data_send
                self.sudo().th_func_create_or_update(val)
            elif action == 'delete':
                self.sudo().th_func_delete(val)


class ThStatusCategory(models.Model):
    _name = "th.status.category"
    _inherit = ["th.status.category", "th.intermediate.table"]
    #
    # related_stt_detail_name = fields.Char(string="Tên trạng thái", related='th_status_detail_ids.name', store=True)
    # related_stt_detail_description = fields.Text(string="Mô tả", related='th_status_detail_ids.th_description', store=True)

    def get_data_sync(self, rec, is_mapping=True):
        data = {
            'name': rec.name if rec.name else False,
            'th_type': rec.th_type if rec.th_type else False,
            'th_description': rec.th_description if rec.th_description else False,
        }
        th_data_send = {
            'name': rec.name if rec.name else False,
            'th_type': rec.th_type if rec.th_type else False,
            'th_description': rec.th_description if rec.th_description else False,
            'th_status_detail_ids': rec.th_status_detail_ids.ids if rec.th_status_detail_ids else False
        } if is_mapping else None
        th_data_send['th_status_detail_ids'] = []
        for i in rec.th_status_detail_ids:
            val = {
                'name': i.name if i.name else False,
                'th_description': i.th_description if i.th_description else False,
            }
            id_detail = self.env['th.status.detail'].th_create_or_update_status_detail(i, 'create_or_update')
            external = self.env['th.mapping.id'].search([('th_model_name', '=', 'th.status.detail'), ('th_internal_id', '=', i.id)], limit=1)
            th_data_send['th_status_detail_ids'].append(external.th_external_id)
        return data, th_data_send

    @api.model
    def th_create_or_update_status_category(self, status_category_data,action):
        _logger.info(f"Create_or_Write Trigger for Status Category: {status_category_data.id}")
        for rec in status_category_data:
            th_data, th_data_send = self.get_data_sync(rec)
            val = {
                'name': rec.name,
                'th_type_sync': 'ontime',
                'th_internal_id': rec.id,
                'th_data': th_data,
                'th_data_send': th_data_send,
                'th_module_id': False,
                'th_system': 'b2b',
            }
            if action == 'create_or_update':
                val['th_data_send'] = th_data_send
                self.sudo().th_func_create_or_update(val)
            elif action == 'delete':
                self.sudo().th_func_delete(val)




class ThOwnershipUnit(models.Model):
    _name = "th.ownership.unit"
    _inherit = ["th.ownership.unit", "th.intermediate.table"]

    def get_data_sync(self, rec, is_mapping=True):
        data = {
            'name': rec.name if rec.name else False,
            'th_code': rec.th_code if rec.th_code else False,
            'th_is_sync': rec.th_is_sync if rec.th_is_sync else False,
            'th_description': rec.th_description if rec.th_description else False,
        }
        data_send = {
            'name': rec.name if rec.name else False,
            'th_code': rec.th_code if rec.th_code else False,
            'th_is_sync': rec.th_is_sync if rec.th_is_sync else False,
            'th_description': rec.th_description if rec.th_description else False,
        } if is_mapping else None
        return data, data_send

    @api.model
    def th_create_or_update_ownership_unit(self, ownership_unit_data=None):
        _logger.info(f"Create_or_Write Trigger for Ownership Unit: {ownership_unit_data.id}")
        for rec in ownership_unit_data:
            th_data, th_data_send = self.get_data_sync(rec)
            val = {
                'name': rec.name,
                'th_type_sync': 'ontime',
                'th_internal_id': rec.id,
                'th_data': th_data,
                'th_data_send': th_data_send,
                'th_module_id': False,
                'th_system': 'b2b',
            }
            self.sudo().th_func_create_or_update(val)

    @api.model
    def th_delete_ownership_unit(self, ownership_unit_data=None):
        _logger.info(f"Delete Trigger for Status Category: {ownership_unit_data.id}")
        for rec in ownership_unit_data:
            th_data, _ = self.get_data_sync(rec)
            val = {
                'name': rec.name,
                'th_type_sync': 'ontime',
                'th_internal_id': rec.id,
                'th_data': th_data,
                'th_system': 'b2b',
            }
            new = self.sudo().th_func_delete(val)


class ThOrigin(models.Model):
    _inherit = "th.origin"

    th_related_major = fields.Many2one(comodel_name="th.major", string="Ngành học",
                                    help="Trường này để kích hoạt trigger khi sửa giá trị trong trường th_university_major_ids.th_major_id",
                                    related='th_university_major_ids.th_major_id', store=True)
    th_related_subject = fields.Many2many('th.subject', string="Môn học", related='th_university_major_ids.th_subject',
                                       help="Trường này để kích hoạt trigger khi sửa giá trị trong trường th_university_major_ids.th_subject")


